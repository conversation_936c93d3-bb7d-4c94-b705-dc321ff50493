# API接口测试用例设计

## 1 用例设计
[按照模块内功能分别设计接口测试用例，如下所示]

### 1.1 用户认证功能
用户登录测试用例见表 1-1 用户登录接口测试用例。

**表 1-1 用户登录接口测试用例**

| 用例名称 | 【用户登录验证】 |
|---------|----------------|
| 接口地址 | /api/user/login |
| 请求方式 | POST |
| 前置条件 | 【用户已注册，系统正常运行】 |

| 用例编号 | 用例标题 | 请求参数 | 预期结果 | 断言 | 是否通过 |
|---------|---------|---------|---------|------|---------|
| login01 | 正确用户名密码登录 | {"username":"testuser","password":"123456"} | 登录成功，返回token | status_code==200 && response.token!=null | ✓ |
| login02 | 错误密码登录 | {"username":"testuser","password":"wrong"} | 登录失败，返回错误信息 | status_code==401 && response.message=="密码错误" | ✓ |
| login03 | 不存在的用户名登录 | {"username":"nouser","password":"123456"} | 登录失败，返回错误信息 | status_code==404 && response.message=="用户不存在" | ✓ |

### 1.2 账户余额查询功能
账户余额查询测试用例见表 1-2 余额查询接口测试用例。

**表 1-2 余额查询接口测试用例**

| 用例名称 | 【账户余额查询】 |
|---------|----------------|
| 接口地址 | /api/account/balance |
| 请求方式 | GET |
| 前置条件 | 【用户已登录，获得有效token】 |

| 用例编号 | 用例标题 | 请求参数 | 预期结果 | 断言 | 是否通过 |
|---------|---------|---------|---------|------|---------|
| balance01 | 有效token查询余额 | Headers: {"Authorization":"Bearer valid_token"} | 成功返回账户余额 | status_code==200 && response.balance>=0 | ✓ |
| balance02 | 无效token查询余额 | Headers: {"Authorization":"Bearer invalid_token"} | 返回认证失败 | status_code==401 && response.message=="token无效" | ✓ |

### 1.3 转账功能
转账功能测试用例见表 1-3 转账接口测试用例。

**表 1-3 转账接口测试用例**

| 用例名称 | 【资金转账操作】 |
|---------|----------------|
| 接口地址 | /api/transfer |
| 请求方式 | POST |
| 前置条件 | 【用户已登录，账户余额充足，收款方账户存在】 |

| 用例编号 | 用例标题 | 请求参数 | 预期结果 | 断言 | 是否通过 |
|---------|---------|---------|---------|------|---------|
| transfer01 | 正常金额转账 | {"to_account":"123456","amount":100.00,"password":"123456"} | 转账成功 | status_code==200 && response.success==true | ✓ |
| transfer02 | 余额不足转账 | {"to_account":"123456","amount":10000.00,"password":"123456"} | 转账失败，余额不足 | status_code==400 && response.message=="余额不足" | ✓ |
| transfer03 | 转账金额为负数 | {"to_account":"123456","amount":-100.00,"password":"123456"} | 转账失败，金额无效 | status_code==400 && response.message=="转账金额必须大于0" | ✓ |

### 1.4 充值功能
充值功能测试用例见表 1-4 充值接口测试用例。

**表 1-4 充值接口测试用例**

| 用例名称 | 【账户充值操作】 |
|---------|----------------|
| 接口地址 | /api/recharge |
| 请求方式 | POST |
| 前置条件 | 【用户已登录，银行卡信息有效】 |

| 用例编号 | 用例标题 | 请求参数 | 预期结果 | 断言 | 是否通过 |
|---------|---------|---------|---------|------|---------|
| recharge01 | 正常金额充值 | {"amount":500.00,"card_number":"****************","cvv":"123"} | 充值成功 | status_code==200 && response.success==true | ✓ |
| recharge02 | 充值金额为0 | {"amount":0.00,"card_number":"****************","cvv":"123"} | 充值失败，金额无效 | status_code==400 && response.message=="充值金额必须大于0" | ✓ |

### 1.5 银行卡管理功能
银行卡管理测试用例见表 1-5 银行卡接口测试用例。

**表 1-5 银行卡接口测试用例**

| 用例名称 | 【银行卡绑定操作】 |
|---------|----------------|
| 接口地址 | /api/bankcard/bind |
| 请求方式 | POST |
| 前置条件 | 【用户已登录，银行卡信息真实有效】 |

| 用例编号 | 用例标题 | 请求参数 | 预期结果 | 断言 | 是否通过 |
|---------|---------|---------|---------|------|---------|
| card01 | 绑定有效银行卡 | {"card_number":"****************","holder_name":"张三","id_card":"123456789012345678"} | 绑定成功 | status_code==200 && response.card_id!=null | ✓ |
| card02 | 绑定无效卡号 | {"card_number":"1234","holder_name":"张三","id_card":"123456789012345678"} | 绑定失败，卡号格式错误 | status_code==400 && response.message=="银行卡号格式错误" | ✓ |
