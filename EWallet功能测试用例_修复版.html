<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EWallet 功能测试用例</title>

    <style>
        body {
            font-family: "SimSun", "宋体", serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            width: 80%;
            margin: 20px auto;
            padding: 30px;
            background-color: #fff;
            border: 1px solid #ddd;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .export-button-container {
            text-align: right;
            margin-bottom: 20px;
        }
        .export-btn {
            padding: 10px 20px;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-left: 10px;
        }
        #export-word-btn {
            background-color: #007bff;
        }
        #export-word-btn:hover {
            background-color: #0056b3;
        }
        #export-html-btn {
            background-color: #28a745;
        }
        #export-html-btn:hover {
            background-color: #1e7e34;
        }
        #markdown-content h1 { font-size: 2em; color: #333; border-bottom: 2px solid #eee; padding-bottom: 0.3em; margin-top: 1.5em; margin-bottom: 1em; }
        #markdown-content h2 { font-size: 1.75em; color: #444; border-bottom: 1px solid #eee; padding-bottom: 0.2em; margin-top: 1.2em; margin-bottom: 0.8em; }
        #markdown-content h3 { font-size: 1.5em; color: #555; margin-top: 1em; margin-bottom: 0.6em; }
        #markdown-content p { margin-bottom: 1em; }
        #markdown-content table { width: 100%; border-collapse: collapse; margin-bottom: 1.5em; font-size: 0.95em; border: 1px solid #ccc; }
        #markdown-content th, #markdown-content td { border: 1px solid #ccc; padding: 10px; text-align: left; vertical-align: top; }
        #markdown-content th { background-color: #f0f0f0; font-weight: bold; }
        #markdown-content td > p:first-child:last-child { margin-bottom: 0; }
        #markdown-content td br { display: block; content: ""; margin-top: 0.5em; }
        #markdown-content ul, #markdown-content ol { margin-left: 20px; padding-left: 20px; }
        #markdown-content li { margin-bottom: 0.5em; }
        .status-message { padding: 10px; margin-bottom: 15px; border-radius: 4px; }
        .status-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <div id="script-status"></div>
        <div class="export-button-container">
            <button id="export-word-btn" class="export-btn">导出为 Word 文档</button>
            <button id="export-html-btn" class="export-btn">导出为 HTML 文档</button>
        </div>
        <div id="markdown-content">
            <p>内容加载中...</p>
        </div>
    </div>

    <!-- 依赖库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js" defer
            onload="console.log('marked.js 已成功加载。')"
            onerror="loadMarkdownFallback();">
    </script>
    <script src="https://unpkg.com/html-docx-js/dist/html-docx.js" defer
            onload="console.log('html-docx-js 已成功加载。'); updateStatus('success', 'Word导出库加载成功');"
            onerror="console.error('html-docx-js 加载失败。'); updateStatus('warning', 'Word导出库加载失败，将使用备用方案');">
    </script>

    <script>
        // 状态更新函数
        function updateStatus(type, message) {
            const statusDiv = document.getElementById('script-status');
            const statusClass = type === 'success' ? 'status-success' :
                               type === 'warning' ? 'status-warning' : 'status-error';
            statusDiv.innerHTML = `<p class="status-message ${statusClass}">${message}</p>`;
        }

        // Markdown备用加载方案
        function loadMarkdownFallback() {
            console.error('marked.js 加载失败，使用备用方案');
            updateStatus('warning', 'Markdown渲染库加载失败，使用简单文本显示');
            // 简单的文本处理备用方案
            window.marked = {
                parse: function(text) {
                    return text.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                }
            };
            initializeContent();
        }

        // 文件下载辅助函数
        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        // Word导出函数
        async function exportToWord() {
            try {
                const contentElement = document.getElementById('markdown-content');
                if (!contentElement) {
                    throw new Error('内容元素未找到');
                }

                // 检查html-docx-js是否可用
                if (typeof htmlDocx !== 'undefined') {
                    // 使用html-docx-js导出
                    const htmlContent = contentElement.innerHTML;
                    const docxContent = htmlDocx.asBlob(htmlContent, {
                        orientation: 'portrait',
                        margins: { top: 720, right: 720, bottom: 720, left: 720 }
                    });

                    downloadFile(docxContent, 'EWallet电子钱包支付系统_个人设置模块_功能测试用例.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
                    updateStatus('success', 'Word文档导出成功！');
                } else {
                    // 备用方案：导出为RTF格式
                    const htmlContent = contentElement.innerHTML;
                    const rtfContent = convertHtmlToRtf(htmlContent);
                    downloadFile(rtfContent, 'EWallet电子钱包支付系统_个人设置模块_功能测试用例.rtf', 'application/rtf');
                    updateStatus('warning', '使用RTF格式导出成功（可用Word打开）！');
                }
            } catch (error) {
                console.error('导出Word文档时出错:', error);
                updateStatus('error', '导出失败: ' + error.message);
                alert('导出Word文档失败: ' + error.message);
            }
        }

        // HTML导出函数
        function exportToHtml() {
            try {
                const contentElement = document.getElementById('markdown-content');
                if (!contentElement) {
                    throw new Error('内容元素未找到');
                }

                const htmlTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>EWallet 功能测试用例</title>
    <style>
        body { font-family: "SimSun", "宋体", serif; line-height: 1.6; margin: 40px; color: #333; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ccc; padding: 10px; text-align: left; vertical-align: top; }
        th { background-color: #f0f0f0; font-weight: bold; }
        h1 { font-size: 2em; color: #333; border-bottom: 2px solid #eee; padding-bottom: 0.3em; }
        h2 { font-size: 1.75em; color: #444; border-bottom: 1px solid #eee; padding-bottom: 0.2em; }
        h3 { font-size: 1.5em; color: #555; }
        p { margin-bottom: 1em; }
    </style>
</head>
<body>
${contentElement.innerHTML}
</body>
</html>`;

                downloadFile(htmlTemplate, 'EWallet电子钱包支付系统_个人设置模块_功能测试用例.html', 'text/html');
                updateStatus('success', 'HTML文档导出成功！');
            } catch (error) {
                console.error('导出HTML文档时出错:', error);
                updateStatus('error', '导出失败: ' + error.message);
                alert('导出HTML文档失败: ' + error.message);
            }
        }

        // 简单的HTML到RTF转换（备用方案）
        function convertHtmlToRtf(html) {
            // 基本的HTML到RTF转换
            let rtf = '{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 SimSun;}}';
            rtf += '\\f0\\fs22 '; // 设置字体和字号

            // 简单的标签替换
            html = html.replace(/<h1[^>]*>(.*?)<\/h1>/gi, '\\par\\fs28\\b $1\\b0\\fs22\\par');
            html = html.replace(/<h2[^>]*>(.*?)<\/h2>/gi, '\\par\\fs24\\b $1\\b0\\fs22\\par');
            html = html.replace(/<h3[^>]*>(.*?)<\/h3>/gi, '\\par\\fs22\\b $1\\b0\\par');
            html = html.replace(/<p[^>]*>(.*?)<\/p>/gi, '\\par $1\\par');
            html = html.replace(/<br[^>]*>/gi, '\\par');
            html = html.replace(/<strong[^>]*>(.*?)<\/strong>/gi, '\\b $1\\b0');
            html = html.replace(/<b[^>]*>(.*?)<\/b>/gi, '\\b $1\\b0');
            html = html.replace(/<[^>]+>/g, ''); // 移除其他HTML标签

            rtf += html + '}';
            return rtf;
        }

        const markdownText = `
# 引言

## 编写目的

统一测试用例编写的规范，为测试执行人员更好执行测试，提高测试效率，最终提高整个EWallet电子钱包支付系统个人设置模块的质量。

## 项目背景

EWallet电子钱包支付系统是一个综合性的移动支付平台，个人设置模块是用户管理个人账户信息、安全设置和支付配置的核心功能模块。本测试用例针对个人设置模块的各项功能进行全面测试验证。

## 参考资料

表1-1参考资料

| 名称 | 备注 |
|------|------|
| EWallet电子钱包支付系统-软件需求规格说明书V1.0 | |
| EWallet电子钱包支付系统-软件测试大纲V1.0 | |
| GBT 15532-2008 计算机软件测试规范 | |
| GBT 9386-2008 计算机软件测试文档编制规范 | |

## 术语和缩略语

表1-2术语/定义

| **术语/定义** | **说明** |
|---------------|----------|
| 支付密码 | 用户进行支付操作时需要输入的6位数字密码 |
| 支付限额 | 用户单次或单日最大支付金额限制 |
| 指纹识别 | 通过用户指纹进行身份验证的生物识别技术 |
| 登录信息 | 用户身份验证相关的个人信息 |

表1-3缩略语

| **缩略语** | **说明** |
|------------|----------|
| EWallet | 电子钱包支付系统 |
| PSM | Personal Setting Module个人设置模块 |
| UI | User Interface用户界面 |
| API | Application Programming Interface应用程序编程接口 |

# 用例设计

## EWallet电子钱包系统-个人设置模块

### 个人设置-修改支付密码功能

修改支付密码功能测试用例见表2-1 修改支付密码测试用例。

表2-1 修改支付密码测试用例

| **用例标识** | PSM-PWD-001 |
|--------------|-------------|
| **功能模块** | 个人设置-修改支付密码 |
| **前置条件** | 1. 用户已成功登录EWallet系统<br/>2. 用户已设置过支付密码<br/>3. 进入个人设置页面 |

| **验证点** | **编号** | **测试步骤** | **期望结果** | **是否通过** |
|------------|----------|--------------|------------|------------|
| 正常修改支付密码 | PSM-PWD-001-01 | 1. 点击"修改支付密码"<br/>2. 输入新支付密码(6位数字)<br/>3. 再次输入支付密码确认<br/>4. 点击"立即修改支付密码"按钮 | 1. 页面跳转到修改支付密码界面<br/>2. 密码输入框正常显示<br/>3. 两次密码输入一致<br/>4. 提示"支付密码修改成功" |  |
| 两次密码不一致 | PSM-PWD-001-02 | 1. 点击"修改支付密码"<br/>2. 输入新支付密码(如:123456)<br/>3. 再次输入不同密码(如:654321)<br/>4. 点击"立即修改支付密码"按钮 | 显示错误提示"两次输入的密码不一致，请重新输入" |  |
| 密码格式验证 | PSM-PWD-001-03 | 1. 点击"修改支付密码"<br/>2. 输入非6位数字密码(如:abc123)<br/>3. 点击"立即修改支付密码"按钮 | 显示错误提示"支付密码必须为6位数字" |  |
| 空密码验证 | PSM-PWD-001-04 | 1. 点击"修改支付密码"<br/>2. 不输入任何密码<br/>3. 点击"立即修改支付密码"按钮 | 显示错误提示"请输入支付密码" |  |
| 返回功能 | PSM-PWD-001-05 | 1. 进入修改支付密码页面<br/>2. 点击左上角返回按钮 | 返回到个人设置页面，不保存任何修改 |  |

**测试结果（需标识清楚是哪条用例的测试结果）**

### 个人设置-修改支付限额功能

修改支付限额功能测试用例见表2-2 修改支付限额测试用例。

表2-2 修改支付限额测试用例

| **用例标识** | PSM-LIMIT-002 |
|--------------|---------------|
| **功能模块** | 个人设置-修改支付限额 |
| **前置条件** | 1. 用户已成功登录EWallet系统<br/>2. 用户已完成实名认证<br/>3. 进入个人设置页面 |

| **验证点** | **编号** | **测试步骤** | **期望结果** | **是否通过** |
|------------|----------|--------------|------------|------------|
| 正常修改支付限额 | PSM-LIMIT-002-01 | 1. 点击"修改支付限额"<br/>2. 输入新的支付限额(如:5000)<br/>3. 点击"立即修改支付限额"按钮 | 1. 进入支付限额设置页面<br/>2. 限额输入框显示当前限额(10000)<br/>3. 成功修改限额<br/>4. 提示"支付限额修改成功" |  |
| 超出最大限额 | PSM-LIMIT-002-02 | 1. 点击"修改支付限额"<br/>2. 输入超过系统最大限额的金额(如:100000)<br/>3. 点击"立即修改支付限额"按钮 | 显示错误提示"支付限额不能超过系统最大限额50000元" |  |
| 输入负数限额 | PSM-LIMIT-002-03 | 1. 点击"修改支付限额"<br/>2. 输入负数金额(如:-1000)<br/>3. 点击"立即修改支付限额"按钮 | 显示错误提示"支付限额必须为正数" |  |
| 输入非数字字符 | PSM-LIMIT-002-04 | 1. 点击"修改支付限额"<br/>2. 输入非数字字符(如:abc)<br/>3. 点击"立即修改支付限额"按钮 | 显示错误提示"请输入有效的数字金额" |  |
| 零限额设置 | PSM-LIMIT-002-05 | 1. 点击"修改支付限额"<br/>2. 输入0<br/>3. 点击"立即修改支付限额"按钮 | 显示错误提示"支付限额不能为0" |  |

**测试结果（需标识清楚是哪条用例的测试结果）**

### 个人设置-安全设置功能

安全设置功能测试用例见表2-3 安全设置测试用例。

表2-3 安全设置测试用例

| **用例标识** | PSM-SECURITY-003 |
|--------------|------------------|
| **功能模块** | 个人设置-安全设置 |
| **前置条件** | 1. 用户已成功登录EWallet系统<br/>2. 设备支持指纹识别功能<br/>3. 进入个人设置页面 |

| **验证点** | **编号** | **测试步骤** | **期望结果** | **是否通过** |
|------------|----------|--------------|------------|------------|
| 启用指纹识别 | PSM-SECURITY-003-01 | 1. 点击"安全设置"<br/>2. 点击"当前设备是否支持指纹识别"开关<br/>3. 按系统提示录入指纹 | 1. 进入安全设置页面<br/>2. 指纹识别开关状态从"未启用"变为"已启用"<br/>3. 指纹录入成功提示 |  |
| 关闭指纹识别 | PSM-SECURITY-003-02 | 1. 点击"安全设置"<br/>2. 点击已启用的"当前设备是否支持指纹识别"开关 | 指纹识别开关状态从"已启用"变为"未启用" |  |
| 启用登录信息录入 | PSM-SECURITY-003-03 | 1. 点击"安全设置"<br/>2. 点击"当前设备是否已录入相关信息"开关 | 登录信息录入开关状态从"未录入"变为"已录入" |  |
| 关闭登录信息录入 | PSM-SECURITY-003-04 | 1. 点击"安全设置"<br/>2. 点击已启用的"当前设备是否已录入相关信息"开关 | 登录信息录入开关状态从"已录入"变为"未录入" |  |
| 设备不支持指纹时的处理 | PSM-SECURITY-003-05 | 1. 在不支持指纹的设备上点击"安全设置"<br/>2. 查看指纹识别选项状态 | 指纹识别选项显示为灰色不可点击状态，提示"当前设备不支持指纹识别" |  |

**测试结果（需标识清楚是哪条用例的测试结果）**

### 个人设置-切换用户功能

切换用户功能测试用例见表2-4 切换用户测试用例。

表2-4 切换用户测试用例

| **用例标识** | PSM-SWITCH-004 |
|--------------|----------------|
| **功能模块** | 个人设置-切换用户 |
| **前置条件** | 1. 用户已成功登录EWallet系统<br/>2. 当前显示用户ID为830000531651<br/>3. 进入个人设置页面 |

| **验证点** | **编号** | **测试步骤** | **期望结果** | **是否通过** |
|------------|----------|--------------|------------|------------|
| 正常切换用户 | PSM-SWITCH-004-01 | 1. 点击"切换用户"<br/>2. 选择其他已登录的用户账号<br/>3. 确认切换 | 1. 显示用户选择列表<br/>2. 成功切换到选中用户<br/>3. 页面显示新用户信息 |  |
| 仅有一个用户时切换 | PSM-SWITCH-004-02 | 1. 在只有一个登录用户的情况下<br/>2. 点击"切换用户" | 提示"当前仅有一个登录用户，无法切换" |  |
| 取消切换操作 | PSM-SWITCH-004-03 | 1. 点击"切换用户"<br/>2. 在用户选择界面点击取消 | 返回个人设置页面，保持当前用户不变 |  |

**测试结果（需标识清楚是哪条用例的测试结果）**

### 个人设置-页面导航功能

页面导航功能测试用例见表2-5 页面导航测试用例。

表2-5 页面导航测试用例

| **用例标识** | PSM-NAV-005 |
|--------------|-------------|
| **功能模块** | 个人设置-页面导航 |
| **前置条件** | 1. 用户已成功登录EWallet系统<br/>2. 处于个人设置页面 |

| **验证点** | **编号** | **测试步骤** | **期望结果** | **是否通过** |
|------------|----------|--------------|------------|------------|
| 底部导航栏功能 | PSM-NAV-005-01 | 1. 点击底部"首页"按钮<br/>2. 点击底部"余额"按钮<br/>3. 点击底部"交易"按钮<br/>4. 点击底部"设置"按钮 | 1. 跳转到首页<br/>2. 跳转到余额页面<br/>3. 跳转到交易页面<br/>4. 保持在设置页面，按钮高亮 |  |
| 页面标题显示 | PSM-NAV-005-02 | 1. 进入个人设置页面<br/>2. 查看页面顶部标题 | 页面顶部正确显示"个人设置"标题 |  |
| 返回主页功能 | PSM-NAV-005-03 | 1. 在个人设置页面<br/>2. 点击左上角返回按钮或手势返回 | 返回到上一级页面或主页 |  |

**测试结果（需标识清楚是哪条用例的测试结果）**
`;

        // 初始化内容函数
        function initializeContent() {
            const markdownContentDiv = document.getElementById('markdown-content');

            if (typeof marked === 'undefined' || typeof marked.parse !== 'function') {
                console.error("marked.js 未正确加载!");
                markdownContentDiv.innerHTML = "<p style='color:red;'>错误：Markdown渲染库未加载。使用纯文本显示。</p><pre>" + markdownText + "</pre>";
                updateStatus('error', 'Markdown渲染库加载失败');
            } else {
                try {
                    marked.setOptions({
                        gfm: true,
                        breaks: true,
                        xhtml: true
                    });
                    const htmlContent = marked.parse(markdownText);
                    markdownContentDiv.innerHTML = htmlContent;
                    updateStatus('success', '内容加载成功');
                } catch (e) {
                    console.error("解析Markdown时出错:", e);
                    markdownContentDiv.innerHTML = "<p style='color:red;'>解析Markdown内容时出错。</p><pre>" + markdownText + "</pre>";
                    updateStatus('error', '内容解析失败');
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 延迟初始化，等待依赖库加载
            setTimeout(initializeContent, 500);

            // 绑定导出按钮事件
            const exportWordBtn = document.getElementById('export-word-btn');
            const exportHtmlBtn = document.getElementById('export-html-btn');

            if (exportWordBtn) {
                exportWordBtn.addEventListener('click', exportToWord);
            }

            if (exportHtmlBtn) {
                exportHtmlBtn.addEventListener('click', exportToHtml);
            }
        });
    </script>
</body>
</html>
